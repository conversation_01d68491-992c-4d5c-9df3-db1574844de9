class PokemonRegionMap_Scene
  def getQuestName(x, y)
    return "" if !@map.point || !@questMap || @mode != 2 || !ARMSettings::ShowQuestIcons || @wallmap
    questName = []
    value = ""
    @questNames = nil
    @questMap.each do |name|
      next if (name[1] != adjustPosX(x) || name[2] != adjustPosY(y))
      return "" if name[4] && !$game_switches[name[4]]
      unless !name[3]
        questName.push($quest_data.getName(name[3].id))
        if questName.length >= 2
          @questNames = questName
          value = _INTL("#{questName.length} Active Quests")
        else
          @questNames = questName
          value = _INTL("Quest: #{questName[0]}")
        end
      else
        value = _INTL("Invalid Quest Position")
      end
    end
    updateButtonInfo if !ARMSettings::ButtonBoxPosition.nil?
    @sprites["modeName"].bitmap.clear
    mapModeSwitchInfo if value == ""
    return value
  end

  def addQuestIconSprites
    usedPositions = {}
    if !@spritesMap["QuestIcons"] && QuestPlugin && ARMSettings::ShowQuestIcons
      @spritesMap["QuestIcons"] = BitmapSprite.new(@mapWidth, @mapHeight, @viewportMap)
      @spritesMap["QuestIcons"].x = @spritesMap["map"].x
      @spritesMap["QuestIcons"].y = @spritesMap["map"].y
      @spritesMap["QuestSelect"] = BitmapSprite.new(@mapWidth, @mapHeight, @viewportMap)
      @spritesMap["QuestSelect"].x = @spritesMap["map"].x
      @spritesMap["QuestSelect"].y = @spritesMap["map"].y
    end
    return if !@spritesMap["QuestIcons"]
    @questMap.each do |index|
      x = adjustPosX(index[1], true, index[0])
      y = adjustPosY(index[2], true, index[0])
      icon = index[5] || ""
      next if usedPositions.key?([x, y, icon])
      next if index[4] && !$game_switches[index[4]]
      @spritesMap["QuestIcons"].z = 50
      pbDrawImagePositions(
        @spritesMap["QuestIcons"].bitmap,
        [["#{Folder}Icons/Quest/mapQuest#{icon}", pointXtoScreenX(x) , pointYtoScreenY(y)]]
      )
      usedPositions[[x, y, icon]] = true
    end
    @spritesMap["QuestIcons"].visible = QuestPlugin && @mode == 2
  end

  def getQuestMapData
    if QuestPlugin && $quest_data
      @questMap = []
      @regionData.each do |region,_|
        @questMap << $quest_data.getQuestMapPositions(@mapPoints, region, @regionData)
      end
      @questMap = @questMap.flatten(1)
    end
  end

  def showQuestInformation(lastChoiceQuest)
    questInfo = @questMap.select { |coords| coords && coords[0..2] == [@region, adjustPosX(@mapX), adjustPosY(@mapY)] }
    questInfo = [] if questInfo.empty? || questInfo[0][3].nil? || (questInfo[0][4] && !$game_switches[questInfo[0][4]])
    return choice = -1 if questInfo.empty?
    input, quest, choice, icon = getCurrentQuestInfo(lastChoiceQuest, questInfo)
    @oldLineCount = @lineCount
    if input && quest
      questInfoText = []
      name = $quest_data.getName(quest.id)
      base = (ARMSettings::QuestInfoTextBase).to_rgb15
      shadow = (ARMSettings::QuestInfoTextShadow).to_rgb15
      description = $quest_data.getStageDescription(quest.id, quest.stage)
      description = _INTL("Not Given") if description.empty?
      location = $quest_data.getStageLocation(quest.id, quest.stage)
      location = "Unknown" if location.empty?
      questInfoText[0] = "<c2=#{base}#{shadow}>Task: #{pbGetMessageFromHash(ScriptTexts, description)}"
      questInfoText[1] = "<c2=#{base}#{shadow}>Location: #{pbGetMessageFromHash(ScriptTexts, location)}"
      @sprites["mapbottom"].previewName = ["Quest: #{name}", @sprites["previewBox"].width]
      if !@sprites["locationText"]
        @sprites["locationText"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
        pbSetSystemFont(@sprites["locationText"].bitmap)
        @sprites["locationText"].visible = false
      end
      @sprites["locationText"].bitmap.clear
      x = 16
      y = 8
      lineHeight = ARMSettings::PreviewLineHeight
      questInfoText.each do |text|
        chars = getFormattedText(@sprites["locationText"].bitmap, x, y, 272, -1, text, lineHeight)
        y += (1 + chars.count { |item| item[0] == "\n" }) * lineHeight
        drawFormattedChars(@sprites["locationText"].bitmap, chars)
        @lineCount = (y / lineHeight)
      end
      @lineCount = ARMSettings::MaxQuestLines if @lineCount > ARMSettings::MaxQuestLines
      getPreviewBox
      @sprites["locationText"].x = Graphics.width - (@sprites["previewBox"].width + UIBorderWidth + ARMSettings::QuestInfoTextOffsetX)
      @sprites["locationText"].y = UIBorderHeight + ARMSettings::QuestInfoTextOffsetY
      @sprites["locationText"].z = 28
    end
    return choice
  end

  def getCurrentQuestInfo(lastChoiceQuest, questInfo)
    @questInfo = questInfo
    if @questNames && @questNames.length >= 2
      choice = messageMap(_INTL("Which quest would you like to view info about?"),
      (<EMAIL>).to_a.map{|i|
        next "#{pbGetMessageFromHash(ScriptTexts, @questNames[i])}"
      }, -1, nil, lastChoiceQuest) { pbUpdate }
      input = choice != -1
      quest = questInfo[choice][3]
    else
      input = true
      quest = questInfo[0][3]
    end
    return input, quest, choice
  end
end

def changeQuestIconChoice(icon)
  @spritesMap["QuestSelect"].bitmap.clear
  icon[4] = "" if icon[4].nil?
  @spritesMap["QuestSelect"].z = 55
  pbDrawImagePositions(
    @spritesMap["QuestSelect"].bitmap,
    [["#{PokemonRegionMap_Scene::Folder}Icons/Quest/mapQuest#{icon[4]}", pointXtoScreenX(adjustPosX(icon[0])) , pointYtoScreenY(adjustPosY(icon[1]))]]
  )
end
