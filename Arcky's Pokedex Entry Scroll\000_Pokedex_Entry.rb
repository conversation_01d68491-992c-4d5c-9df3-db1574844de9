class PokemonPokedexInfo_Scene
  unless Essentials::VERSION.include?("20")
    def drawPageInfo
      @sprites["infosprite"].visible = true if PluginManager.installed?("Modular UI Scenes")
      @sprites["background"].setBitmap(_INTL("Graphics/UI/Pokedex/bg_info"))
      @sprites["overlay"].bitmap.clear
      drawPageIcons if PluginManager.installed?("Modular UI Scenes")
      overlay = @sprites["overlay"].bitmap
      base   = Color.new(88, 88, 80)
      shadow = Color.new(168, 184, 184)
      imagepos = []
      imagepos.push([_INTL("Graphics/UI/Pokedex/overlay_info"), 0, 0]) if @brief
      species_data = GameData::Species.get_species_form(@species, @form)
      # Write various bits of text
      indexText = "???"
      if @dexlist[@index][:number] > 0
        indexNumber = @dexlist[@index][:number]
        indexNumber -= 1 if @dexlist[@index][:shift]
        indexText = sprintf("%03d", indexNumber)
      end
      textpos = [
        [_INTL("{1}{2} {3}", indexText, " ", species_data.name),
        246, 48, :left, Color.new(248, 248, 248), Color.black]
      ]
      if @show_battled_count
        textpos.push([_INTL("Number Battled"), 314, 164, :left, base, shadow])
        textpos.push([$player.pokedex.battled_count(@species).to_s, 452, 196, :right, base, shadow])
      else
        textpos.push([_INTL("Height"), 314, 164, :left, base, shadow])
        textpos.push([_INTL("Weight"), 314, 196, :left, base, shadow])
      end
      if $player.owned?(@species)
        # Write the category
        textpos.push([_INTL("{1} Pokémon", species_data.category), 246, 80, :left, base, shadow])
        # Write the height and weight
        if !@show_battled_count
          height = species_data.height
          weight = species_data.weight
          if System.user_language[3..4] == "US"   # If the user is in the United States
            inches = (height / 0.254).round
            pounds = (weight / 0.45359).round
            textpos.push([_ISPRINTF("{1:d}'{2:02d}\"", inches / 12, inches % 12), 460, 164, :right, base, shadow])
            textpos.push([_ISPRINTF("{1:4.1f} lbs.", pounds / 10.0), 494, 196, :right, base, shadow])
          else
            textpos.push([_ISPRINTF("{1:.1f} m", height / 10.0), 470, 164, :right, base, shadow])
            textpos.push([_ISPRINTF("{1:.1f} kg", weight / 10.0), 482, 196, :right, base, shadow])
          end
        end
        # Draw the Pokédex entry text
        chars = getFormattedText(overlay, 40, 246, Graphics.width - 80, -1, species_data.pokedex_entry, 32)
        text = chars.map { |ch| ch[0] }.join
        @textLines = text.split("\n")
        numLines = @textLines.length
        @textIndex = 0 if !@textIndex || @previousSpecies != @species
        @previousSpecies = @species
        @entryText = @textLines[@textIndex...(@textIndex + 4)].join(' ')
        drawTextEx(overlay, 40, 246, Graphics.width - 80, 4,   # overlay, x, y, width, num lines
                      @entryText, base, shadow)
        # Draw the footprint
        footprintfile = GameData::Species.footprint_filename(@species, @form)
        if footprintfile
          footprint = RPG::Cache.load_bitmap("", footprintfile)
          overlay.blt(226, 138, footprint, footprint.rect)
          footprint.dispose
        end
        # Show the owned icon
        imagepos.push(["Graphics/UI/Pokedex/icon_own", 212, 44])
        # Draw the type icon(s)
        species_data.types.each_with_index do |type, i|
          type_number = GameData::Type.get(type).icon_position
          type_rect = Rect.new(0, type_number * 32, 96, 32)
          overlay.blt(296 + (100 * i), 120, @typebitmap.bitmap, type_rect)
        end
      else
        # Write the category
        textpos.push([_INTL("????? Pokémon"), 246, 80, :left, base, shadow])
        # Write the height and weight
        if !@show_battled_count
          if System.user_language[3..4] == "US"   # If the user is in the United States
            textpos.push([_INTL("???'??\""), 460, 164, :right, base, shadow])
            textpos.push([_INTL("????.? lbs."), 494, 196, :right, base, shadow])
          else
            textpos.push([_INTL("????.? m"), 470, 164, :right, base, shadow])
            textpos.push([_INTL("????.? kg"), 482, 196, :right, base, shadow])
          end
        end
      end
      # Draw all text
      pbDrawTextPositions(overlay, textpos)
      # Draw all images
      pbDrawImagePositions(overlay, imagepos)
    end
  else
    def drawPageInfo
      @sprites["background"].setBitmap(_INTL("Graphics/Pictures/Pokedex/bg_info"))
      overlay = @sprites["overlay"].bitmap
      @sprites["overlay"].bitmap.clear
      base   = Color.new(88, 88, 80)
      shadow = Color.new(168, 184, 184)
      imagepos = []
      if @brief
        imagepos.push([_INTL("Graphics/Pictures/Pokedex/overlay_info"), 0, 0])
      end
      species_data = GameData::Species.get_species_form(@species, @form)
      # Write various bits of text
      indexText = "???"
      if @dexlist[@index][4] > 0
        indexNumber = @dexlist[@index][4]
        indexNumber -= 1 if @dexlist[@index][5]
        indexText = sprintf("%03d", indexNumber)
      end
      textpos = [
        [_INTL("{1}{2} {3}", indexText, " ", species_data.name),
         246, 48, 0, Color.new(248, 248, 248), Color.new(0, 0, 0)]
      ]
      if @show_battled_count
        textpos.push([_INTL("Number Battled"), 314, 164, 0, base, shadow])
        textpos.push([$player.pokedex.battled_count(@species).to_s, 452, 196, 1, base, shadow])
      else
        textpos.push([_INTL("Height"), 314, 164, 0, base, shadow])
        textpos.push([_INTL("Weight"), 314, 196, 0, base, shadow])
      end
      if $player.owned?(@species)
        # Write the category
        textpos.push([_INTL("{1} Pokémon", species_data.category), 246, 80, 0, base, shadow])
        # Write the height and weight
        if !@show_battled_count
          height = species_data.height
          weight = species_data.weight
          if System.user_language[3..4] == "US"   # If the user is in the United States
            inches = (height / 0.254).round
            pounds = (weight / 0.45359).round
            textpos.push([_ISPRINTF("{1:d}'{2:02d}\"", inches / 12, inches % 12), 460, 164, 1, base, shadow])
            textpos.push([_ISPRINTF("{1:4.1f} lbs.", pounds / 10.0), 494, 196, 1, base, shadow])
          else
            textpos.push([_ISPRINTF("{1:.1f} m", height / 10.0), 470, 164, 1, base, shadow])
            textpos.push([_ISPRINTF("{1:.1f} kg", weight / 10.0), 482, 196, 1, base, shadow])
          end
        end
        # Draw the Pokédex entry text
        chars = getFormattedText(overlay, 40, 246, Graphics.width - 80, -1, species_data.pokedex_entry, 32)
        text = chars.map { |ch| ch[0] }.join
        @textLines = text.split("\n")
        numLines = @textLines.length
        @textIndex = 0 if !@textIndex || @previousSpecies != @species
        @previousSpecies = @species
        @entryText = @textLines[@textIndex...(@textIndex + 4)].join(' ')
        drawTextEx(overlay, 40, 246, Graphics.width - 80, 4,   # overlay, x, y, width, num lines
                      @entryText, base, shadow)
        # Draw the footprint
        footprintfile = GameData::Species.footprint_filename(@species, @form)
        if footprintfile
          footprint = RPG::Cache.load_bitmap("", footprintfile)
          overlay.blt(226, 138, footprint, footprint.rect)
          footprint.dispose
        end
        # Show the owned icon
        imagepos.push(["Graphics/Pictures/Pokedex/icon_own", 212, 44])
        # Draw the type icon(s)
        species_data.types.each_with_index do |type, i|
          type_number = GameData::Type.get(type).icon_position
          type_rect = Rect.new(0, type_number * 32, 96, 32)
          overlay.blt(296 + (100 * i), 120, @typebitmap.bitmap, type_rect)
        end
      else
        # Write the category
        textpos.push([_INTL("????? Pokémon"), 246, 80, 0, base, shadow])
        # Write the height and weight
        if !@show_battled_count
          if System.user_language[3..4] == "US"   # If the user is in the United States
            textpos.push([_INTL("???'??\""), 460, 164, 1, base, shadow])
            textpos.push([_INTL("????.? lbs."), 494, 196, 1, base, shadow])
          else
            textpos.push([_INTL("????.? m"), 470, 164, 1, base, shadow])
            textpos.push([_INTL("????.? kg"), 482, 196, 1, base, shadow])
          end
        end
      end
      # Draw all text
      pbDrawTextPositions(overlay, textpos)
      # Draw all images
      pbDrawImagePositions(overlay, imagepos)
    end
  end

  def makeScrollArrows
    @sprites["mapArrowUpText"] = AnimatedSprite.new(findUsableUI("mapArrowUp"), 8, 28, 40, 2, @viewport)
    @sprites["mapArrowUpText"].x = (Graphics.width / 2) - 14
    @sprites["mapArrowUpText"].y = Graphics.height - (32 * 4) - 40
    @sprites["mapArrowUpText"].play
    @sprites["mapArrowUpText"].visible = false
    @sprites["mapArrowDownText"] = AnimatedSprite.new(findUsableUI("mapArrowDown"), 8, 28, 40, 2, @viewport)
    @sprites["mapArrowDownText"].x = (Graphics.width / 2) - 14
    @sprites["mapArrowDownText"].y = Graphics.height - 35
    @sprites["mapArrowDownText"].play
    @sprites["mapArrowDownText"].visible = false
  end

  def pbPokedexEntryTextScroll
    makeScrollArrows
    if @textLines.length > 4
      pbPlayCursorSE
    else
      return
    end
    loop do
      Graphics.update
      Input.update
      pbUpdate
      if Input.trigger?(Input::BACK)
        @sprites["mapArrowUpText"].visible = false
        @sprites["mapArrowDownText"].visible = false
        break
      elsif Input.trigger?(Input::DOWN)
        next if @textIndex + 1 > @textLines.length - 4
        @textIndex += 1
        @entryText = @textLines[@textIndex...@textIndex + 4].join(' ')
        drawPageInfo
      elsif Input.trigger?(Input::UP)
        next if @textIndex - 1 < 0
        @textIndex -= 1
        @entryText = @textLines[@textIndex...@textIndex + 4].join(' ')
        drawPageInfo
      end
      @sprites["mapArrowUpText"].visible = @textIndex > 0
      @sprites["mapArrowDownText"].visible = @textIndex < @textLines.length - 4
    end
  end

  unless Essentials::VERSION.include?("20")
    if PluginManager.installed?("Modular UI Scenes")
      def pbScene
        Pokemon.play_cry(@species, @form)
        loop do
          Graphics.update
          Input.update
          pbUpdate
          dorefresh = false
          if Input.trigger?(Input::ACTION)
            pbSEStop
            Pokemon.play_cry(@species, @form) if @page == 1
          elsif Input.trigger?(Input::SPECIAL)
            ret = pbPageCustomUse(@page_id) if @page_id == :page_info
          elsif Input.trigger?(Input::BACK)
            pbPlayCloseMenuSE
            break
          elsif Input.trigger?(Input::USE)
            ret = pbPageCustomUse(@page_id) if @page_id == :page_area
            if !ret
              case @page_id
              when :page_info
                pbPlayDecisionSE
                @show_battled_count = !@show_battled_count
                dorefresh = true
              when :page_forms
                if @available.length > 1
                  pbPlayDecisionSE
                  pbChooseForm
                  dorefresh = true
                end
              end
            else
              dorefresh = true
            end
          elsif Input.repeat?(Input::UP)
            oldindex = @index
            pbGoToPrevious
            if @index != oldindex
              pbUpdateDummyPokemon
              @available = pbGetAvailableForms
              pbSEStop
              (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.repeat?(Input::DOWN)
            oldindex = @index
            pbGoToNext
            if @index != oldindex
              pbUpdateDummyPokemon
              @available = pbGetAvailableForms
              pbSEStop
              (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.repeat?(Input::LEFT)
            oldpage = @page
            numpages = @page_list.length
            @page -= 1
            @page = numpages if @page < 1
            @page = 1 if @page > numpages
            if @page != oldpage
              pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.repeat?(Input::RIGHT)
            oldpage = @page
            numpages = @page_list.length
            @page += 1
            @page = numpages if @page < 1
            @page = 1 if @page > numpages
            if @page != oldpage
              pbPlayCursorSE
              dorefresh = true
            end
          end
          drawPage(@page) if dorefresh
        end
        return @index
      end

      alias _dex_entry_pbPageCustomUse pbPageCustomUse
      def pbPageCustomUse(page_id)
        if page_id == :page_info
          pbPokedexEntryTextScroll
          return true
        end
        return _dex_entry_pbPageCustomUse(page_id)
      end
    else # Modular UI scenes not installed
      def pbScene
        Pokemon.play_cry(@species, @form)
        @mapMovement = false
        new_x = 0
        new_y = 0
        ox = 0
        oy = 0
        distPerFrame = System.uptime
        loop do
          Graphics.update
          Input.update
          pbUpdate
          dorefresh = false
          if ox != 0 || oy != 0
            if ox != 0
              @sprites["areamap"].x = lerp(new_x - ox, new_x, 0.1, distPerFrame, System.uptime)
              @sprites["areahighlight"].x = @sprites["areamap"].x
              ox = 0 if @sprites["areamap"].x == new_x
            end
            if oy != 0
              @sprites["areamap"].y = lerp(new_y - oy, new_y, 0.1, distPerFrame, System.uptime)
              @sprites["areahighlight"].y = @sprites["areamap"].y
              oy = 0 if @sprites["areamap"].y == new_y
            end
            next if ox != 0 || oy != 0
          end
          if @mapMovement
            @sprites["mapArrowUp"].visible = -(@mapY * 16) < 0 ? true : false
            @sprites["mapArrowDown"].visible = -(@mapY * 16) > @mapMaxY ? true : false
            @sprites["mapArrowLeft"].visible = -(@mapX * 16) < 0 ? true : false
            @sprites["mapArrowRight"].visible = -(@mapX * 16) > @mapMaxX ? true : false
          end
          if Input.trigger?(Input::ACTION)
            pbSEStop
            Pokemon.play_cry(@species, @form) if @page == 1
          elsif Input.trigger?(Input::SPECIAL)
            pbPokedexEntryTextScroll if @page == 1
          elsif Input.trigger?(Input::BACK)
            if @mapMovement
              @mapMovement = false
              @sprites["mapArrowUp"].visible = false
              @sprites["mapArrowDown"].visible = false
              @sprites["mapArrowLeft"].visible = false
              @sprites["mapArrowRight"].visible = false
            else
              pbPlayCloseMenuSE
              break
            end
          elsif Input.trigger?(Input::USE)
            case @page
            when 1   # Info
              @show_battled_count = !@show_battled_count
              @mapMovement = false
              dorefresh = true
            when 2   # Area
              pbPlayCursorSE
              @mapMovement = true if !@noArea && @sprites["areamap"].bitmap.width > 480
              dorefresh = true
            when 3   # Forms
              if @available.length > 1
                pbPlayDecisionSE
                @mapMovement = false
                pbChooseForm
                dorefresh = true
              end
            end
          elsif !@mapMovement
            if Input.trigger?(Input::UP)
              oldindex = @index
              pbGoToPrevious
              if @index != oldindex
                pbUpdateDummyPokemon
                @available = pbGetAvailableForms
                pbSEStop
                (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
                dorefresh = true
              end
            elsif Input.trigger?(Input::DOWN)
              oldindex = @index
              pbGoToNext
              if @index != oldindex
                pbUpdateDummyPokemon
                @available = pbGetAvailableForms
                pbSEStop
                (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
                dorefresh = true
              end
            elsif Input.trigger?(Input::LEFT)
              oldpage = @page
              @page -= 1
              @page = 1 if @page < 1
              @page = 3 if @page > 3
              if @page != oldpage
                pbPlayCursorSE
                dorefresh = true
              end
            elsif Input.trigger?(Input::RIGHT)
              oldpage = @page
              @page += 1
              @page = 1 if @page < 1
              @page = 3 if @page > 3
              if @page != oldpage
                pbPlayCursorSE
                dorefresh = true
              end
            end
          else
            case Input.dir8
            when 1, 2, 3
              if -(@mapY * 16) > @mapMaxY
                @mapY += 1
                oy = -1 * PokemonRegionMap_Scene::SQUARE_HEIGHT
                new_y = @sprites["areamap"].y + oy
                distPerFrame = System.uptime
              end
            when 7, 8, 9
              if -(@mapY * 16) < 0
                @mapY -= 1
                oy = 1 * PokemonRegionMap_Scene::SQUARE_HEIGHT
                new_y = @sprites["areamap"].y + oy
                distPerFrame = System.uptime
              end
            end
            case Input.dir8
            when 1, 4, 7
              if -(@mapX * 16) < 0
                @mapX -= 1
                ox = 1 * PokemonRegionMap_Scene::SQUARE_WIDTH
                new_x = @sprites["areamap"].x + ox
                distPerFrame = System.uptime
              end
            when 3, 6, 9
              if -(@mapX * 16) > @mapMaxX
                @mapX += 1
                ox = -1 * PokemonRegionMap_Scene::SQUARE_WIDTH
                new_x = @sprites["areamap"].x + ox
                distPerFrame = System.uptime
              end
            end
          end
          if dorefresh
            drawPage(@page)
          end
        end
        return @index
      end
    end
  else
    def pbScene
      Pokemon.play_cry(@species, @form)
      @mapMovement = false
      new_x = @sprites["areamap"].x
      new_y = @sprites["areamap"].y
      ox = 0
      oy = 0
      dist_per_frame = 8 * 20 / Graphics.frame_rate
      loop do
        Graphics.update
        Input.update
        pbUpdate
        dorefresh = false
        if ox != 0 || oy != 0
          ox += (ox > 0) ? -dist_per_frame : (ox < 0) ? dist_per_frame : 0
          oy += (oy > 0) ? -dist_per_frame : (oy < 0) ? dist_per_frame : 0
          @sprites["areamap"].x = new_x - ox
          @sprites["areamap"].y = new_y - oy
          @sprites["areahighlight"].x = @sprites["areamap"].x
          @sprites["areahighlight"].y = @sprites["areamap"].y
          next
        end
        if @mapMovement
          @sprites["upArrow"].visible = -(@mapY * 16) < 0
          @sprites["downArrow"].visible = -(@mapY * 16) > @mapMaxY
          @sprites["leftArrow"].visible = -(@mapX * 16) < 0
          @sprites["rightArrow"].visible = -(@mapX * 16) > @mapMaxX
        end
        if Input.trigger?(Input::ACTION)
          pbSEStop
          Pokemon.play_cry(@species, @form) if @page == 1
        elsif Input.trigger?(Input::SPECIAL)
          pbPokedexEntryTextScroll if @page == 1
        elsif Input.trigger?(Input::BACK)
          if @mapMovement
            @mapMovement = false
            @sprites["upArrow"].visible = false
            @sprites["downArrow"].visible = false
            @sprites["leftArrow"].visible = false
            @sprites["rightArrow"].visible = false
          else
            pbPlayCloseMenuSE
            break
          end
        elsif Input.trigger?(Input::USE)
          case @page
          when 1   # Info
            @show_battled_count = !@show_battled_count
            @mapMovement = false
            dorefresh = true
          when 2   # Area
            pbPlayCursorSE
            @mapMovement = true if !@noArea && (@sprites["areamap"].bitmap.width > (Graphics.width - BEHIND_UI[1]) || @sprites["areamap"].bitmap.height > (Graphics.height - BEHIND_UI[3]))
            makeMapArrows if !@sprites["upArrow"] && !@noArea
            dorefresh = true
          when 3   # Forms
            if @available.length > 1
              pbPlayDecisionSE
              @mapMovement = false
              pbChooseForm
              dorefresh = true
            end
          end
        elsif !@mapMovement
          if Input.trigger?(Input::UP)
            oldindex = @index
            pbGoToPrevious
            if @index != oldindex
              pbUpdateDummyPokemon
              @available = pbGetAvailableForms
              pbSEStop
              (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.trigger?(Input::DOWN)
            oldindex = @index
            pbGoToNext
            if @index != oldindex
              pbUpdateDummyPokemon
              @available = pbGetAvailableForms
              pbSEStop
              (@page == 1) ? Pokemon.play_cry(@species, @form) : pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.trigger?(Input::LEFT)
            oldpage = @page
            @page -= 1
            @page = 1 if @page < 1
            @page = 3 if @page > 3
            if @page != oldpage
              pbPlayCursorSE
              dorefresh = true
            end
          elsif Input.trigger?(Input::RIGHT)
            oldpage = @page
            @page += 1
            @page = 1 if @page < 1
            @page = 3 if @page > 3
            if @page != oldpage
              pbPlayCursorSE
              dorefresh = true
            end
          end
        else
          case Input.dir8
          when 1, 2, 3
            if -(@mapY * 16) > @mapMaxY
              @mapY += 1
              oy = -1 * ARMSettings::SQUARE_HEIGHT
              new_y = @sprites["areamap"].y + oy
            end
          when 7, 8, 9
            if -(@mapY * 16) < 0
              @mapY -= 1
              oy = 1 * ARMSettings::SQUARE_HEIGHT
              new_y = @sprites["areamap"].y + oy
            end
          end
          case Input.dir8
          when 1, 4, 7
            if -(@mapX * 16) < 0
              @mapX -= 1
              ox = 1 * ARMSettings::SQUARE_WIDTH
              new_x = @sprites["areamap"].x + ox
            end
          when 3, 6, 9
            if -(@mapX * 16) > @mapMaxX
              @mapX += 1
              ox = -1 * ARMSettings::SQUARE_WIDTH
              new_x = @sprites["areamap"].x + ox
            end
          end
        end
        if dorefresh
          drawPage(@page)
        end
      end
      return @index
    end
  end
end
