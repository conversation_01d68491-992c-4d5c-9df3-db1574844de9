module FollowingPkmn
  #-----------------------------------------------------------------------------
  @@can_refresh = false
  #-----------------------------------------------------------------------------
  # Checks if the Following Pokemon is active and following the player
  #-----------------------------------------------------------------------------
  def self.active?; return @@can_refresh; end
  #-----------------------------------------------------------------------------
  # Checks whether Following Pokemon data should be accessed or no
  #-----------------------------------------------------------------------------
  def self.can_check?
    return false if !$game_temp || !$PokemonGlobal || !$player
    return false if !$game_temp.respond_to?(:followers) || !$game_temp.followers
    return false if !$PokemonGlobal.respond_to?(:followers) || !$PokemonGlobal.followers
    return false if !$player.respond_to?(:party) || !$player.party
    return false if !$scene.is_a?(Scene_Map)
    return true
  end
  #-----------------------------------------------------------------------------
  # Refresh Following Pokemon's visibility when Following the player
  #-----------------------------------------------------------------------------
  def self.refresh_internal
    if !FollowingPkmn.can_check? || !FollowingPkmn.get || !$PokemonGlobal.follower_toggled
      @@can_refresh = false
      return
    end
    old_refresh = @@can_refresh
    refresh     = false
    first_pkmn  = FollowingPkmn.get_pokemon
    if first_pkmn
      refresh = EventHandlers.trigger_2(:following_pkmn_appear, first_pkmn)
      refresh = true if refresh == -1
    end
    @@can_refresh = refresh
    $PokemonGlobal.call_refresh[1] = true if old_refresh != @@can_refresh && !$PokemonGlobal.call_refresh[1] 
  end
  #-----------------------------------------------------------------------------
  # Raises The Current Following Pokemon's Happiness by 3-5 and
  # checks for hold item
  #-----------------------------------------------------------------------------
  def self.increase_time
    return if !FollowingPkmn.can_check?
    delta = Graphics.delta
    $PokemonGlobal.follower_timers.each_key { |key| $PokemonGlobal.follower_timers[key] += delta }
    return if $PokemonGlobal.follower_timers[:friendship] < FollowingPkmn::FRIENDSHIP_TIME_TAKEN
    FollowingPkmn.get_pokemon&.changeHappiness("levelup")
    $PokemonGlobal.follower_timers[:friendship] = 0
  end
  #-----------------------------------------------------------------------------
  # Script Command for Following Pokémon finding an item in the field
  #-----------------------------------------------------------------------------
  def self.item(item, quantity = 1, message = nil)
    return false if !FollowingPkmn.can_check?
    foll_name = FollowingPkmn.get_pokemon&.name
    message   = _INTL("{1} seems to be holding something...") if nil_or_empty?(message)
    pbMessage(_INTL(message, foll_name))
    item = GameData::Item.get(item)
    return false if !item || quantity < 1
    item_name = (quantity > 1) ? item.portion_name_plural : item.portion_name
    pocket    = item.pocket
    move      = item.move
    if $bag.add(item, quantity)   # If item can be picked up
      me_name = (item.is_key_item?) ? "Key item get" : "Item get"
      if item == :DNASPLICERS
        pbMessage("\\me[#{me_name}]" + _INTL("{2} found \\c[1]{1}\\c[0]!", item_name, foll_name) + "\\wtnp[30]")
      elsif item.is_machine?   # TM or HM
        if quantity > 1
          pbMessage("\\me[#{me_name}]" + _INTL("{4} found {1} \\c[1]{2} {3}\\c[0]!",
                                              quantity, item_name, GameData::Move.get(move).name, foll_name) + "\\wtnp[30]")
        else
          pbMessage("\\me[#{me_name}]" + _INTL("{3} found \\c[1]{1} {2}\\c[0]!",
                                              item_name, GameData::Move.get(move).name, foll_name) + "\\wtnp[30]")
        end
      elsif quantity > 1
        pbMessage("\\me[#{me_name}]" + _INTL("{3} found {1} \\c[1]{2}\\c[0]!", quantity, item_name, foll_name) + "\\wtnp[30]")
      elsif item_name.starts_with_vowel?
        pbMessage("\\me[#{me_name}]" + _INTL("{2} found an \\c[1]{1}\\c[0]!", item_name, foll_name) + "\\wtnp[30]")
      else
        pbMessage("\\me[#{me_name}]" + _INTL("{2} found a \\c[1]{1}\\c[0]!", item_name, foll_name) + "\\wtnp[30]")
      end
      pbMessage(_INTL("You put the {1} in\\nyour Bag's <icon=bagPocket{2}>\\c[1]{3}\\c[0] pocket.",
                      item_name, pocket, PokemonBag.pocket_names[pocket - 1]))
      $PokemonGlobal.follower_timers[:item] = 0
      return true
    end
    # Can't add the item
    if item.is_machine?   # TM or HM
      if quantity > 1
        pbMessage(_INTL("{4} found {1} \\c[1]{2} {3}\\c[0]!", quantity, item_name, GameData::Move.get(move).name, foll_name) + "\\wtnp[30]")
      else
        pbMessage(_INTL("{3} found \\c[1]{1} {2}\\c[0]!", item_name, GameData::Move.get(move).name, foll_name) + "\\wtnp[30]")
      end
    elsif quantity > 1
      pbMessage(_INTL("{3} found {1} \\c[1]{2}\\c[0]!", quantity, item_name, foll_name) + "\\wtnp[30]")
    elsif item_name.starts_with_vowel?
      pbMessage(_INTL("{2} found an \\c[1]{1}\\c[0]!", item_name, foll_name) + "\\wtnp[30]")
    else
      pbMessage(_INTL("{2} found a \\c[1]{1}\\c[0]!", item_name, foll_name) + "\\wtnp[30]")
    end
    pbMessage(_INTL("But your Bag is full..."))
    return false
  end
  #-----------------------------------------------------------------------------
  # Check if the Following Pokemon can be spoken to, or not
  #-----------------------------------------------------------------------------
  def self.can_talk?(interact = false)
    return false if !FollowingPkmn.can_check?
    return false if !$game_temp || $game_temp.in_battle || $game_temp.in_menu
    return false if FollowingPkmn.get_event.move_route_forcing
    return false if $game_player.move_route_forcing
    facing = pbFacingTile
    if !FollowingPkmn.active? || !$game_map.passable?(facing[1], facing[2], $game_player.direction, $game_player)
      if interact
        $game_player.straighten
        EventHandlers.trigger(:on_player_interact)
      end
      return false
    end
    return true
  end
  #-----------------------------------------------------------------------------
end
