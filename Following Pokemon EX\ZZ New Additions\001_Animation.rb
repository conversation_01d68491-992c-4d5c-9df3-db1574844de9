#-------------------------------------------------------------------------------
# Main framework for new event animations
#-------------------------------------------------------------------------------
class EventAnimation
  def initialize(event, duration)
    @event    = event
    @viewport = Spriteset_Map.viewport
    @sprites  = {}
    if @event.is_a?(Game_Player)
      @event_sprite = $scene.spritesetGlobal.playersprite
    elsif @event.is_a?(Game_Follower)
      sprites = $scene.spritesetGlobal.follower_sprites.sprites
      @event_sprite = sprites.select { |spr| spr.character == event }.first
      @event_sprite.follower_animating = true
    else
      sprites = $scene.spritesets[event.map_id].character_sprites
      @event_sprite = sprites.select { |spr| spr.character == event }.first
    end
    @start_time = System.uptime
    @duration   = duration
    @disposed   = false
  end

  def disposed?; return @disposed; end

  def dispose
    pbDisposeSpriteHash(@sprites)
    @disposed = true
    @event_sprite.follower_animating = false if @event_sprite.respond_to?(:follower_animating)
  end

  def progress
    ret = (System.uptime - @start_time) / @duration
    ret = 1.0 if ret >= 1.0
    return ret
  end

  def add_sprite(key, sprite)
    sprite.viewport = @viewport
    if sprite.bitmap
      sprite.ox = sprite.src_rect.width / 2
      sprite.oy = sprite.src_rect.height
    end
    @sprites[key] = sprite
  end

  def update
    return if disposed?
    update_sprites
    dispose if progress >= 1.0
  end

  def update_sprites
    pbUpdateSpriteHash(@sprites)
    @sprites.each_value do |sprite|
      sprite.x = @event_sprite.x
      sprite.y = @event_sprite.y
      sprite.z = @event_sprite.z
      sprite.tone = @event_sprite.tone
      sprite.opacity = @event_sprite.opacity
      sprite.visible = @event_sprite.visible
    end
  end
end

#-------------------------------------------------------------------------------
# New Follower Come Out Animation
#-------------------------------------------------------------------------------
class Follower_ComeOutAnimation < EventAnimation
  include Battle::Scene::Animation::BallAnimationMixin

  SHINY_FRAME_DURATION = 0.2
  ANIMATION_SPLIT_POINT = 0.6

  def initialize(event, duration, pkmn)
    super(event, duration)
    @shiny = pkmn.shiny?
    @played_shiny = false
    sprite = Sprite.new
    sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", "Follower_ComeOut")
    sprite.src_rect.width = sprite.bitmap.height
    @ball_frames = sprite.bitmap.width / sprite.bitmap.height
    add_sprite("ball_effect", sprite)
    @sprites["ball_effect"].oy = (sprite.bitmap.height / 4) * 3
    sprite = Sprite.new
    sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", "Follower_ShinySparkle")
    sprite.src_rect.width = sprite.bitmap.height
    @shiny_frames = sprite.bitmap.width / sprite.bitmap.height
    add_sprite("shiny_sparkle", sprite)
    sprite = Sprite.new
    ball_name = "Follower_Ball_#{pkmn.poke_ball}"
    if pbResolveBitmap("Graphics/Animations/Follower/#{ball_name}")
      sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", ball_name)
    else
      sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", "Follower_Ball_POKEBALL")
    end
    add_sprite("ball_sprite", sprite)
    @event_sprite.visible = false
    @event_sprite.zoom_x = 0
    @event_sprite.zoom_y = 0
    @event_sprite.color = getBattlerColorFromPokeBall(pkmn.poke_ball)
    @event_sprite.color.alpha = 255
    pbSEPlay("Battle recall")
    FollowingPkmn.change_sprite(FollowingPkmn.get_pokemon)
  end

  def update_sprites
    super
    ball_effect = @sprites["ball_effect"]
    shiny_effect = @sprites["shiny_sparkle"]
    ball_sprite = @sprites["ball_sprite"]
    if progress < ANIMATION_SPLIT_POINT
      phase_progress = progress / ANIMATION_SPLIT_POINT
      frame_idx = (phase_progress * @ball_frames).to_i
      frame_idx = [@ball_frames - 1, frame_idx].min
      ball_effect.src_rect.x = frame_idx * ball_effect.bitmap.height
      zoom = 0.25 + (phase_progress * 0.75)
      zoom = 0 if frame_idx == 0
      @event_sprite.zoom_x = zoom
      @event_sprite.zoom_y = zoom
      shiny_effect.visible = false
      ball_sprite.visible = frame_idx == 0
      ball_effect.visible = @event_sprite.visible
      @event_sprite.color.alpha = 255 * (1 - phase_progress)
    else
      ball_effect.visible = false
      ball_sprite.visible = false
      @event_sprite.zoom_x = 1.0
      @event_sprite.zoom_y = 1.0
      @event_sprite.color.alpha = 0
      if @shiny && !@played_shiny
        @played_shiny = true
        pbSEPlay("Anim/Shiny sparkle", 50)
      end
      if @shiny
        shiny_effect.visible = @event_sprite.visible
        frame_idx = (System.uptime / SHINY_FRAME_DURATION).to_i % @shiny_frames
        shiny_effect.src_rect.x = frame_idx * shiny_effect.bitmap.height
      else
        shiny_effect.visible = false
      end
    end
  end
end

#-------------------------------------------------------------------------------
# New Follower Go In Animation
#-------------------------------------------------------------------------------
class Follower_GoInAnimation < EventAnimation
  include Battle::Scene::Animation::BallAnimationMixin

  ANIMATION_SPLIT_POINT = 0.8

  def initialize(event, duration, pkmn)
    super(event, duration)
    @pkmn = pkmn
    sprite = Sprite.new
    sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", "Follower_ComeOut")
    sprite.src_rect.width = sprite.bitmap.height
    @ball_frames = sprite.bitmap.width / sprite.bitmap.height
    add_sprite("ball_effect", sprite)
    @sprites["ball_effect"].oy = (sprite.bitmap.height / 4) * 3
    sprite = Sprite.new
    ball_name = "Follower_Ball_#{pkmn.poke_ball}"
    if pbResolveBitmap("Graphics/Animations/Follower/#{ball_name}")
      sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", ball_name)
    else
      sprite.bitmap = RPG::Cache.load_bitmap("Graphics/Animations/Follower/", "Follower_Ball_POKEBALL")
    end
    add_sprite("ball_sprite", sprite)
    @sprites["ball_sprite"].visible = false
    @event_sprite.zoom_x = 1.0
    @event_sprite.zoom_y = 1.0
    @event_sprite.color = getBattlerColorFromPokeBall(pkmn.poke_ball)
    @event_sprite.color.alpha = 0
    pbSEPlay("Battle recall")
  end

  def dispose
    super
    FollowingPkmn.remove_sprite
  end

  def update_sprites
    super
    ball_effect = @sprites["ball_effect"]
    ball_sprite = @sprites["ball_sprite"]
    if progress < ANIMATION_SPLIT_POINT
      phase_progress = progress / ANIMATION_SPLIT_POINT
      @event_sprite.color.alpha = 255 * phase_progress
      frame_idx = ((1.0 - phase_progress) * @ball_frames).to_i.clamp(0, @ball_frames - 1)
      zoom = 1.0 - (phase_progress * 0.75)
      zoom = 0 if frame_idx == 0
      @event_sprite.zoom_x = zoom
      @event_sprite.zoom_y = zoom
      ball_effect.src_rect.x = frame_idx * ball_effect.bitmap.height
      ball_sprite.visible = (frame_idx == 0)
    else
      @event_sprite.visible = false
      ball_effect.visible = false
      ball_sprite.visible = true
      phase_progress = (progress - ANIMATION_SPLIT_POINT) / (1.0 - ANIMATION_SPLIT_POINT)
      ball_sprite.opacity = 255 * (1.0 - phase_progress)
    end
  end
end

#-------------------------------------------------------------------------------
# Expose useful properties
#-------------------------------------------------------------------------------
class Spriteset_Global
  attr_reader :playersprite
  attr_reader :follower_sprites
end

class Scene_Map
  attr_reader :spritesets
end

class Spriteset_Map
  attr_reader :character_sprites
end

class FollowerSprites
  attr_reader :sprites
end

class Sprite_Character
  attr_accessor :follower_animating
end
