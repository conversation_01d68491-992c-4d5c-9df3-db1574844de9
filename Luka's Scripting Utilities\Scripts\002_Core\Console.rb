#===============================================================================
#  Extensions for the `Console` module
#===============================================================================
module ::Console
  class << self
    #---------------------------------------------------------------------------
    #  function to echo to console without line break
    #---------------------------------------------------------------------------
    def echo_str(msg, options = {})
      echo markup_style(markup(msg), **options)
    end
    #---------------------------------------------------------------------------
    #  extend paragraph echo
    #---------------------------------------------------------------------------
    def echo_p(msg, options = {})
      echoln markup_style(markup(msg), **options)
    end
    #---------------------------------------------------------------------------
  end
end
