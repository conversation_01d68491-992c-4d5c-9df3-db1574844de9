class GlowFlySprite < Sprite
  attr_accessor :origin_x
  attr_accessor :origin_y
  attr_accessor :max_x
  attr_accessor :max_y

  def initialize(viewport)
    super
    @origin_x = 0
    @origin_y = 0
    @max_x    = 0
    @max_y    = 0
    self.opacity = rand(0..255)
    @frame_count = 0
    reset_movement
    reset_fading
    update
  end

  def reset_movement
    @move_start = @frame_count
    @start_x    = @end_x || rand(-@max_x..@max_x)
    @start_y    = @end_y || rand(-@max_y..@max_y)
    @end_x      = rand(-@max_x..@max_x)
    @end_y      = rand(-@max_y..@max_y)
    @move_dur   = rand(10..30) * 6  # Convert duration from seconds to frames (assuming 60 FPS)
  end

  def reset_fading
    @fade_start = @frame_count
    @start_opac = self.opacity
    @end_opac = self.opacity >= 128 ? rand(0..50) : rand(200..255)
    @fade_dur = rand(10..40) * 6  # Convert duration from seconds to frames (assuming 60 FPS)
  end

  def update
    super
    @frame_count += 1
    cur_frame = @frame_count
    factor = (cur_frame - @move_start).to_f / @move_dur
    factor = 1.0 if factor > 1.0
    offset_x = @start_x + (@end_x - @start_x) * factor
    offset_y = @start_y + (@end_y - @start_y) * factor
    self.x = @origin_x + offset_x
    self.y = @origin_y + offset_y
    self.ox = (self.bitmap&.width || 0) / 2
    self.oy = (self.bitmap&.height || 0) / 2
    factor = (cur_frame - @fade_start).to_f / @fade_dur
    factor = 1.0 if factor > 1.0
    calc_opac = @start_opac + (@end_opac - @start_opac) * factor
    self.opacity = calc_opac
    reset_movement if offset_x == @end_x && offset_y == @end_y
    reset_fading if calc_opac == @end_opac
    self.visible
  end
end

class GlowEffect_GlowFly < GlowEffect
  attr_accessor :fade_speed
  
  def initialize(viewport, map, event)
    super
    @name = pbEventCommentInput(event, 1, "GlowEvent").first.split(",").last.strip
    @sprites = {}
    count = rand(3..7)
    count.times do |i|
      key = "spr_#{i}"
      @sprites[key] = GlowFlySprite.new(viewport)
      @sprites[key].bitmap = RPG::Cache.load_bitmap("Graphics/Characters/Glows/", "GlowFly_#{@name}")
      @sprites[key].max_x  = Game_Map::TILE_WIDTH * (rand(3..10) / 10.0 + 0.25)
      @sprites[key].max_y  = Game_Map::TILE_HEIGHT * (rand(3..10) / 10.0 + 0.25)
    end
    update
  end  

  def update
    super
    self.y  -= (Game_Map::TILE_HEIGHT * @event_h)
    @sprites&.each_value do |s|
      s.origin_x = self.x
      s.origin_y = self.y
      s.z        = self.z
      s.visible  = self.visible
      s.update
    end
  end

  def dispose
    super
    pbDisposeSpriteHash(@sprites)
  end
end