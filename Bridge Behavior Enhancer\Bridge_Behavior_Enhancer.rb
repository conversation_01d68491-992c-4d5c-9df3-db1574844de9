#===============================================================================
# Bridge Behavior Enhancer (Rewritten)
# - Event name tags to control drawing above bridges
# - Skips interactions across bridge layers
# - Restricts [BridgeOver] events with random movement to bridge tiles only
# Essentials style, Ruby only. No core file edits required.
# Usage tags in event name:
#   [BridgeOver]  -> Draw above the bridge deck (when bridge is not active)
#                    Random move routes are restricted to bridge tiles only
#   [BridgeUnder] -> Draw beneath the bridge deck (when bridge is active)
#                    Random move routes are restricted to bridge tiles only
#===============================================================================

module BridgeBehaviorEnhancer
  OVER_TAG  = /\[BridgeOver\]/i
  UNDER_TAG = /\[BridgeUnder\]/i

  def self.bridge_active?
    return ($PokemonGlobal && $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0)
  end

  # Checks whether a map tile is a bridge tile by scanning layers for a bridge tag
  def self.bridge_tile_at?(map, x, y)
    return false if !map || !map.valid?(x, y)
    [2, 1, 0].each do |i|
      tid = map.data[x, y, i] || 0
      next if tid == 0
      ttag = GameData::TerrainTag.try_get(map.terrain_tags[tid])
      next if !ttag || ttag.ignore_passability
      return true if ttag.bridge
    end
    return false
  end

  def self.mode_for(event)
    return (event.respond_to?(:bridge_display_mode)) ? event.bridge_display_mode : nil
  end

  def self.interactable?(event)
    mode = mode_for(event)
    return true if !mode
    # Always allow interaction for player-initiated events (Action Button, Player Touch)
    # Only restrict autonomous triggers (Autorun, Parallel Process)
    return true if event.trigger == 0 || event.trigger == 1  # Action Button or Player Touch
    # For autonomous triggers, check bridge layer compatibility
    if bridge_active?
      return mode != :under
    else
      return mode != :over
    end
  end

  def self.player_treats_event_as_through?(event)
    mode = mode_for(event)
    return false if !mode
    return (bridge_active? ? mode == :under : mode == :over)
  end

  # Backward compatibility for older plugins
  def self.should_treat_as_through?(event)
    return player_treats_event_as_through?(event)
  end
end

#===============================================================================
# TilemapRenderer: Modify bridge deck z when active to allow "under" layering
#===============================================================================
if defined?(TilemapRenderer)
  class TilemapRenderer
    alias __bbe_refresh_tile_z__ refresh_tile_z unless method_defined?(:__bbe_refresh_tile_z__)
    def refresh_tile_z(tile, map, y, layer, tile_id)
      __bbe_refresh_tile_z__(tile, map, y, layer, tile_id)
      # When bridge is active, raise the deck slightly above ground to allow "under" events
      if tile.bridge && $PokemonGlobal && $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
        tile.z = 10  # Place deck above ground but well below characters
      end
    end
  end
end

module BridgeBehaviorEnhancer

  # Check if moving from current position to new position would leave bridge tiles
  def self.would_leave_bridge?(event, direction)
    return false if !event
    current_x, current_y = event.x, event.y
    new_x = current_x + (direction == 6 ? 1 : direction == 4 ? -1 : 0)
    new_y = current_y + (direction == 2 ? 1 : direction == 8 ? -1 : 0)

    # Check if current position is on bridge and new position is not
    current_on_bridge = bridge_tile_at?(event.map, current_x, current_y)
    new_on_bridge = bridge_tile_at?(event.map, new_x, new_y)

    return current_on_bridge && !new_on_bridge
  end

end

#===============================================================================
# Game_Event: parse tags from name
#===============================================================================
class Game_Event < Game_Character
  attr_reader :bridge_display_mode  # :over, :under, or nil

  alias __bbe_refresh__ refresh
  def refresh
    __bbe_refresh__
    @bridge_display_mode = nil
    nm = (self.name || "")
    if nm =~ BridgeBehaviorEnhancer::OVER_TAG
      @bridge_display_mode = :over
    elsif nm =~ BridgeBehaviorEnhancer::UNDER_TAG
      @bridge_display_mode = :under
    end
  end

  # Prevent autonomous/touch triggers across bridge layers
  if instance_methods.include?(:check_event_trigger_touch)
    alias __bbe_touch__ check_event_trigger_touch
    def check_event_trigger_touch(dir)
      return if !BridgeBehaviorEnhancer.interactable?(self)
      __bbe_touch__(dir)
    end
  end
  if instance_methods.include?(:check_event_trigger_after_turning)
    alias __bbe_after_turn__ check_event_trigger_after_turning
    def check_event_trigger_after_turning
      return if !BridgeBehaviorEnhancer.interactable?(self)
      __bbe_after_turn__
    end
  end
  if instance_methods.include?(:check_event_trigger_after_moving)
    alias __bbe_after_move__ check_event_trigger_after_moving
    def check_event_trigger_after_moving
      return if !BridgeBehaviorEnhancer.interactable?(self)
      __bbe_after_move__
    end
  end

  # Override movement methods to restrict random movement to bridge tiles
  alias __bbe_move_random__ move_random
  def move_random
    if (@bridge_display_mode == :over || @bridge_display_mode == :under)
      # Try each direction randomly, but only allow moves that stay on bridge tiles
      directions = [2, 4, 6, 8].shuffle
      directions.each do |direction|
        if !BridgeBehaviorEnhancer.would_leave_bridge?(self, direction)
          case direction
          when 2 then move_down
          when 4 then move_left
          when 6 then move_right
          when 8 then move_up
          end
          return
        end
      end
      # If no valid bridge direction found, don't move
      return
    end
    __bbe_move_random__
  end

  alias __bbe_move_toward_player__ move_toward_player
  def move_toward_player
    if (@bridge_display_mode == :over || @bridge_display_mode == :under)
      # Calculate direction toward player
      sx = distance_x_from($game_player.x)
      sy = distance_y_from($game_player.y)
      direction = 0
      if sx.abs > sy.abs
        direction = sx > 0 ? 4 : 6
      elsif sy != 0
        direction = sy > 0 ? 8 : 2
      end
      # Only move if it doesn't leave bridge tiles
      if direction != 0 && !BridgeBehaviorEnhancer.would_leave_bridge?(self, direction)
        case direction
        when 2 then move_down
        when 4 then move_left
        when 6 then move_right
        when 8 then move_up
        end
        return
      end
      # If can't move toward player without leaving bridge, try random bridge movement
      # Call the restricted random movement directly to avoid recursion
      directions = [2, 4, 6, 8].shuffle
      directions.each do |direction|
        if !BridgeBehaviorEnhancer.would_leave_bridge?(self, direction)
          case direction
          when 2 then move_down
          when 4 then move_left
          when 6 then move_right
          when 8 then move_up
          end
          return
        end
      end
      return
    end
    __bbe_move_toward_player__
  end

  alias __bbe_move_away_from_player__ move_away_from_player
  def move_away_from_player
    if (@bridge_display_mode == :over || @bridge_display_mode == :under)
      # Calculate direction away from player
      sx = distance_x_from($game_player.x)
      sy = distance_y_from($game_player.y)
      direction = 0
      if sx.abs > sy.abs
        direction = sx > 0 ? 6 : 4
      elsif sy != 0
        direction = sy > 0 ? 2 : 8
      end
      # Only move if it doesn't leave bridge tiles
      if direction != 0 && !BridgeBehaviorEnhancer.would_leave_bridge?(self, direction)
        case direction
        when 2 then move_down
        when 4 then move_left
        when 6 then move_right
        when 8 then move_up
        end
        return
      end
      # If can't move away from player without leaving bridge, try random bridge movement
      # Call the restricted random movement directly to avoid recursion
      directions = [2, 4, 6, 8].shuffle
      directions.each do |direction|
        if !BridgeBehaviorEnhancer.would_leave_bridge?(self, direction)
          case direction
          when 2 then move_down
          when 4 then move_left
          when 6 then move_right
          when 8 then move_up
          end
          return
        end
      end
      return
    end
    __bbe_move_away_from_player__
  end
end

#===============================================================================
# Sprite_Character: layering above/below bridge deck
#===============================================================================
class Sprite_Character < RPG::Sprite
  alias __bbe_update__ update
  def update
    __bbe_update__
    ch = @character
    return if !ch || !ch.is_a?(Game_Event)
    mode = ch.bridge_display_mode
    return if !mode
    on_bridge = BridgeBehaviorEnhancer.bridge_tile_at?(ch.map, ch.x, ch.y)
    active   = BridgeBehaviorEnhancer.bridge_active?
    case mode
    when :over
      # Raise above bridge overlays only when bridge layer is inactive
      self.z = [self.z, 50_000].max if on_bridge && !active
    when :under
      # Place beneath the bridge deck only while on a bridge and the bridge is active
      if on_bridge && active
        # Bridge deck is now at z = 10, ground is at z = 0
        # Place "under" events between ground and deck
        self.z = 5
      end
    end
  end
end

#===============================================================================
# Dynamic shadow compatibility (Sprite_Shadow / Overworld Shadow sprites)
#===============================================================================
if defined?(Sprite_Shadow)
  class Sprite_Shadow < RPG::Sprite
    alias __bbe_shadow_update__ update
    def update
      __bbe_shadow_update__
      ev = @character
      return if !ev || !ev.is_a?(Game_Event)
      if ev.bridge_display_mode == :over
        self.z = ScreenPosHelper.pbScreenZ(ev, @ch) - 1
      elsif ev.bridge_display_mode == :under
        if BridgeBehaviorEnhancer.bridge_active? && BridgeBehaviorEnhancer.bridge_tile_at?(ev.map, ev.x, ev.y)
          self.z = 4  # Below the "under" event (which is at z = 5)
        end
      end
    end
  end
end

if defined?(Sprite_Character) && defined?(Sprite_Character::ShadowSprite)
  class Sprite_Character
    class ShadowSprite
      alias __bbe_shadow_pos__ position unless method_defined?(:__bbe_shadow_pos__)
      def position
        __bbe_shadow_pos__
        ev = @event
        return unless ev && ev.is_a?(Game_Event)
        active   = BridgeBehaviorEnhancer.bridge_active?
        on_bridge = BridgeBehaviorEnhancer.bridge_tile_at?(ev.map, ev.x, ev.y)
        if ev.name =~ BridgeBehaviorEnhancer::OVER_TAG
          if on_bridge && !active
            @sprite.z = [@character.z - 1, 49_999].max
          else
            @sprite.z = @character.z - 1
          end
        elsif ev.name =~ BridgeBehaviorEnhancer::UNDER_TAG
          if on_bridge && active
            @sprite.z = 4  # Below the "under" event (which is at z = 5)
          else
            @sprite.z = @character.z - 1
          end
        end
      rescue
      end
    end
  end
end

#===============================================================================
# Movement collision: treat events across bridge plane as through (player only)
#===============================================================================
class Game_Player < Game_Character
  alias __bbe_player_passable__ passable?
  def passable?(x, y, d, strict = false)
    new_x = x + (d == 6 ? 1 : d == 4 ? -1 : 0)
    new_y = y + (d == 2 ? 1 : d == 8 ? -1 : 0)
    return false unless self.map.valid?(new_x, new_y)
    return true if @through
    # Allow debug through mode (Ctrl key) to bypass all collision checks
    return true if $DEBUG && Input.press?(Input::CTRL)
    if strict
      return false unless self.map.passableStrict?(x, y, d, self)
      return false unless self.map.passableStrict?(new_x, new_y, 10 - d, self)
    else
      return false unless self.map.passable?(x, y, d, self)
      return false unless self.map.passable?(new_x, new_y, 10 - d, self)
    end
    # Event collisions with bridge exceptions for player only
    self.map.events.each_value do |ev|
      next if self == ev || !ev.at_coordinate?(new_x, new_y) || ev.through
      # Allow player to pass through events on different bridge planes
      if BridgeBehaviorEnhancer.player_treats_event_as_through?(ev)
        next
      end
      return false if ev.character_name != ""
    end
    return true
  end
end

#===============================================================================
# Interaction filtering (player-initiated and distance triggers)
#===============================================================================
class Game_Player < Game_Character
  alias __bbe_face__ pbFacingEvent
  def pbFacingEvent(ignoreInterpreter = false)
    ev = __bbe_face__(ignoreInterpreter)
    return nil if ev && !BridgeBehaviorEnhancer.interactable?(ev)
    return ev
  end

  alias __bbe_here__ check_event_trigger_here
  def check_event_trigger_here(triggers)
    return false if $game_system.map_interpreter.running?
    result = false
    $game_map.events.each_value do |ev|
      next if !ev.at_coordinate?(@x, @y)
      next if !triggers.include?(ev.trigger)
      next if ev.jumping? || !ev.over_trigger?
      next if !BridgeBehaviorEnhancer.interactable?(ev)
      ev.start
      result = true if ev.starting
    end
    return result
  end

  alias __bbe_there__ check_event_trigger_there
  def check_event_trigger_there(triggers)
    return false if $game_system.map_interpreter.running?
    result = false
    new_x = @x + (@direction == 6 ? 1 : @direction == 4 ? -1 : 0)
    new_y = @y + (@direction == 2 ? 1 : @direction == 8 ? -1 : 0)
    return false if !$game_map.valid?(new_x, new_y)
    $game_map.events.each_value do |ev|
      next if !triggers.include?(ev.trigger)
      next if !ev.at_coordinate?(new_x, new_y)
      next if ev.jumping? || ev.over_trigger?
      next if !BridgeBehaviorEnhancer.interactable?(ev)
      ev.start
      result = true if ev.starting
    end
    if !result && $game_map.counter?(new_x, new_y)
      new_x += (@direction == 6 ? 1 : @direction == 4 ? -1 : 0)
      new_y += (@direction == 2 ? 1 : @direction == 8 ? -1 : 0)
      return false if !$game_map.valid?(new_x, new_y)
      $game_map.events.each_value do |ev|
        next if !triggers.include?(ev.trigger)
        next if !ev.at_coordinate?(new_x, new_y)
        next if ev.jumping? || ev.over_trigger?
        next if !BridgeBehaviorEnhancer.interactable?(ev)
        ev.start
        result = true if ev.starting
      end
    end
    return result
  end

  alias __bbe_touch__ check_event_trigger_touch
  def check_event_trigger_touch(dir)
    return false if $game_system.map_interpreter.running?
    result = false
    xo = (dir == 4) ? -1 : (dir == 6) ? 1 : 0
    yo = (dir == 8) ? -1 : (dir == 2) ? 1 : 0
    $game_map.events.each_value do |ev|
      next if ![1, 2].include?(ev.trigger)
      next if !ev.at_coordinate?(@x + xo, @y + yo)
      if ev.name[/(?:sight|trainer)\((\d+)\)/i]
        dist = $~[1].to_i
        next if !pbEventCanReachPlayer?(ev, self, dist)
      elsif ev.name[/counter\((\d+)\)/i]
        dist = $~[1].to_i
        next if !pbEventFacesPlayer?(ev, self, dist)
      end
      next if ev.jumping? || ev.over_trigger?
      next if !BridgeBehaviorEnhancer.interactable?(ev)
      ev.start
      result = true if ev.starting
    end
    return result
  end

  alias __bbe_trainers__ pbTriggeredTrainerEvents
  def pbTriggeredTrainerEvents(triggers, checkIfRunning = true, trainer_only = false)
    arr = __bbe_trainers__(triggers, checkIfRunning, trainer_only)
    return arr.select { |e| BridgeBehaviorEnhancer.interactable?(e) }
  end

  alias __bbe_counters__ pbTriggeredCounterEvents
  def pbTriggeredCounterEvents(triggers, checkIfRunning = true)
    arr = __bbe_counters__(triggers, checkIfRunning)
    return arr.select { |e| BridgeBehaviorEnhancer.interactable?(e) }
  end

  alias __bbe_distance__ pbCheckEventTriggerFromDistance
  def pbCheckEventTriggerFromDistance(triggers)
    events = pbTriggeredTrainerEvents(triggers)
    events.concat(pbTriggeredCounterEvents(triggers))
    return false if events.length == 0
    ret = false
    events.each do |ev|
      next if !BridgeBehaviorEnhancer.interactable?(ev)
      ev.start
      ret = true if ev.starting
    end
    return ret
  end
end



