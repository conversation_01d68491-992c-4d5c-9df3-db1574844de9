class TrainerBadgeCase_Scene
  def pbStartScene
    @viewport = Viewport.new(0, 0, Graphics.width, Graphics.height)
    @viewport.z = 99999
    @sprites = {}

    # Determine badge case image based on player's gender
    if $player.male?
      top_image = "Graphics/UI/BadgeCase/BadgeCaseTop"
      bottom_image = "Graphics/UI/BadgeCase/BadgeCaseBottom"
      button_image = "Graphics/UI/BadgeCase/Button"
    else
      top_image = "Graphics/UI/BadgeCase/BadgeCaseTop_f"
      bottom_image = "Graphics/UI/BadgeCase/BadgeCaseBottom_f"
      button_image = "Graphics/UI/BadgeCase/Button_f"
    end

    # Load Badge Case bottom (background)
    @sprites["badgecasebottom"] = IconSprite.new(0, 0, @viewport)
    @sprites["badgecasebottom"].setBitmap(bottom_image)
    @sprites["badgecasebottom"].z = 0

    # Load Badge Case top cover
    @sprites["badgecasetop"] = IconSprite.new(0, 0, @viewport)
    @sprites["badgecasetop"].setBitmap(top_image)
    @sprites["badgecasetop"].y = 0
    @sprites["badgecasetop"].z = 2  # Top cover layer

    # Load button sprite
    @sprites["button"] = IconSprite.new(224, 310, @viewport)
    @sprites["button"].setBitmap(button_image)
    @sprites["button"].src_rect.set(0, 0, 64, 62)
    @sprites["button"].z = 1

    # Load gym leader icons
    pbLoadGymLeaderIcons

    # Display badges
    @sprites["overlay"] = BitmapSprite.new(Graphics.width, Graphics.height, @viewport)
    @sprites["overlay"].z = 1
    pbDrawBadges(@sprites["overlay"].bitmap)

    pbFadeInAndShow(@sprites) { pbUpdate }
  end

  # Load Gym Leader Icons
  def pbLoadGymLeaderIcons
    # Base positions for each icon (x, y)
    icon_positions = [
      [48, 64],    # Leader 0
      [160, 64],   # Leader 1
      [272, 64],   # Leader 2 (+2)
      [384, 64],   # Leader 3 (+2)
      [48, 176],   # Leader 4 (+2)
      [160, 176],  # Leader 5 (+2)
      [272, 176],  # Leader 6 (+2)
      [384, 176]   # Leader 7 (+2)
    ]

    icon_width = 80   # Each icon is 80x80
    icon_height = 80

    8.times do |i|
      # Check if player has met the gym leader or has the badge
      has_met = $player.respond_to?(:met_gym_leader?) ? $player.met_gym_leader?(i) : false
      has_met = has_met || $player.badges[i]  # Also show as met if they have the badge

      if has_met
        icon_path = "Graphics/UI/BadgeCase/gymleadericon_ismet"
      else
        icon_path = "Graphics/UI/BadgeCase/gymleadericon_notmet"
      end

      x_pos = icon_positions[i][0]
      y_pos = icon_positions[i][1]

      @sprites["gymleader#{i}"] = IconSprite.new(x_pos, y_pos, @viewport)
      @sprites["gymleader#{i}"].setBitmap(icon_path)
      @sprites["gymleader#{i}"].src_rect.set(
        (i % 4) * icon_width,    # X position in spritesheet (4 columns)
        (i / 4) * icon_height,   # Y position in spritesheet (2 rows)
        icon_width,              # Width to grab from spritesheet
        icon_height             # Height to grab from spritesheet
      )
      @sprites["gymleader#{i}"].z = 3
    end

    # Store initial positions for animation
    @icon_positions = icon_positions.map { |pos| pos.dup }
  end

  def pbUpdate
    pbUpdateSpriteHash(@sprites)
  end

  def pbDrawBadges(bitmap)
    badge_width = 95
    badge_height = 95
    x = 42
    y = 74
    x_spacing = 110
    y_spacing = 109

    imagePositions = []
    8.times do |i|
      if $player.badges[i]
        x_pos = x + (i % 4) * x_spacing
        # Add 2 pixels to x position for all badges except the first one (i == 0)
        x_pos += 2 if i > 0
        y_pos = y + (i / 4) * y_spacing
        badge_x = (i % 4) * badge_width
        badge_y = (i / 4) * badge_height
        imagePositions.push(["Graphics/UI/BadgeCase/Badges", x_pos, y_pos, badge_x, badge_y, badge_width, badge_height])
      end
    end

    pbDrawImagePositions(bitmap, imagePositions)
  end

  def pbOpenBadgeCase
    badge_case_top_height = @sprites["badgecasetop"].bitmap.height
    total_move = badge_case_top_height
    duration = 0.5  # Animation duration in seconds

    animateButton(true)

    timer_start = System.uptime
    loop do
      time_passed = System.uptime - timer_start
      progress = time_passed / duration
      break if progress >= 1.0

      ease_progress = easing_out(progress)
      move_amount = -badge_case_top_height * ease_progress

      @sprites["badgecasetop"].y = move_amount
      8.times { |j| @sprites["gymleader#{j}"].y = @icon_positions[j][1] + move_amount }

      Graphics.update
      pbUpdate
    end

    @sprites["badgecasetop"].y = -badge_case_top_height
    8.times { |j| @sprites["gymleader#{j}"].y = @icon_positions[j][1] - badge_case_top_height }

    Graphics.update
    pbUpdate
  end

  def pbCloseBadgeCase
    badge_case_top_height = @sprites["badgecasetop"].bitmap.height
    duration = 0.5  # Animation duration in seconds

    animateButton(false)

    timer_start = System.uptime
    loop do
      time_passed = System.uptime - timer_start
      progress = time_passed / duration
      break if progress >= 1.0

      ease_progress = easing_in(progress)
      move_amount = -badge_case_top_height + (badge_case_top_height * ease_progress)

      @sprites["badgecasetop"].y = move_amount
      8.times { |j| @sprites["gymleader#{j}"].y = @icon_positions[j][1] + move_amount }

      Graphics.update
      pbUpdate
    end

    @sprites["badgecasetop"].y = 0
    8.times { |j| @sprites["gymleader#{j}"].y = @icon_positions[j][1] }

    Graphics.update
    pbUpdate
  end

  def easing_out(t)
    return 1 - (1 - t) ** 3
  end

  def easing_in(t)
    return t ** 3
  end

  def pbBadgeCase
    badge_case_open = false
    loop do
      Graphics.update
      Input.update
      pbUpdate

      if Input.trigger?(Input::USE) && !badge_case_open
        pbPlayDecisionSE
        pbOpenBadgeCase
        badge_case_open = true
      elsif Input.trigger?(Input::BACK)
        if badge_case_open
          pbPlayCloseMenuSE
          pbCloseBadgeCase
          badge_case_open = false
        else
          break  # Only break the loop if the case is already closed
        end
      end
    end
  end

  def pbEndScene
    pbPlayCloseMenuSE
    pbFadeOutAndHide(@sprites) { pbUpdate }
    pbDisposeSpriteHash(@sprites)
    @viewport.dispose
  end

  def animateButton(opening)
    frames = [[0, 0], [64, 0], [128, 0]]
    animation_order = opening ? [0, 1, 2, 1, 0] : [0, 1, 2, 1, 0]
    frame_duration = 0.067  # About 4 frames at 60 FPS (4/60 ≈ 0.067 seconds)

    animation_order.each do |frame|
      @sprites["button"].src_rect.set(frames[frame][0], frames[frame][1], 64, 62)
      Graphics.update
      pbUpdate

      # Wait for frame_duration seconds
      timer_start = System.uptime
      loop do
        Graphics.update
        Input.update
        pbUpdate
        break if System.uptime - timer_start >= frame_duration
      end
    end
  end
end

class TrainerBadgeCaseScreen
  def initialize(scene)
    @scene = scene
  end

  def pbStartScreen
    @scene.pbStartScene
    @scene.pbBadgeCase
    @scene.pbEndScene
  end
end
