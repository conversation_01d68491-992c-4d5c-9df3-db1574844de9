class OverworldFlyAnimation
  
  # List of characters in the game
  CHARACTER_PLAYER = [
    # Boy (default)
    [
      "malefieldmove_0",  # "Graphics/Characters/"
      "malefieldmove_1",  # "Graphics/Characters/"
      "flyBirdBoyFront",      # "Graphics/Pictures/"
      "flyBirdBoyBack"        # "Graphics/Pictures/"
    ],
    # Girl (default)
    [
      "femalefieldmove_0", # "Graphics/Characters/"
      "femalefieldmove_1", # "Graphics/Characters/"
      "flyBirdGirlFront",     # "Graphics/Pictures/"
      "flyBirdGirlBack"       # "Graphics/Pictures/"
    ]
  ]

  CHARACTER_BIRD = "flyBird" # Bird character "Graphics/Pictures/"
  SE_BIRD = "Fly bird"       # Bird sound effect "Audio/SE/"
end
