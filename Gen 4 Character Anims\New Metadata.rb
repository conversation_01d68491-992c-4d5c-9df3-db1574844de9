module GameData
  class PlayerMetadata

    SCHEMA = {
      "SectionName"      => [:id,                "u"],
      "TrainerType"      => [:trainer_type,      "e", :TrainerType],
      "WalkCharset"      => [:walk_charset,      "s"],
      "RunCharset"       => [:run_charset,       "s"],
      "CycleCharset"     => [:cycle_charset,     "s"],
      "CycleStopCharset" => [:cycle_stop_charset, "s"],
      "SurfCharset"      => [:surf_charset,      "s"],
      "SurfJumpCharset"  => [:surf_jump_charset, "s"],
      "DiveCharset"      => [:dive_charset,      "s"],
      "FishCharset"      => [:fish_charset,      "s"],
      "SurfFishCharset"  => [:surf_fish_charset, "s"],
      "SavingCharset"    => [:saving_charset,    "s"],
      "FieldMoveCharset" => [:field_move_charset, "s"],
      "WateringCharset"  => [:watering_charset,  "s"],
      "HealingCharset"   => [:healing_charset,   "s"],
      "Home"             => [:home,              "vuuu"]
    }

    alias __gen4_anim__initialize initialize unless private_method_defined?(:__gen4_anim__initialize)
    def initialize(*args)
      __gen4_anim__initialize(*args)
      hash = args[0]
      if hash.is_a?(Hash)
        @cycle_stop_charset = hash[:cycle_stop_charset]
        @saving_charset     = hash[:saving_charset]
        @field_move_charset = hash[:field_move_charset]
        @watering_charset   = hash[:watering_charset]
        @healing_charset    = hash[:healing_charset]
        @surf_jump_charset  = hash[:surf_jump_charset]
      end
    end

    def cycle_stop_charset
      return @cycle_stop_charset || cycle_charset
    end

    def saving_charset
      return @saving_charset || run_charset
    end

    def healing_charset
      return @healing_charset || run_charset
    end

    def surf_jump_charset
      return @surf_jump_charset || surf_charset
    end
  end
end

module Compiler
  def self.compile_metadata(path = "PBS/metadata.txt")
    compile_pbs_file_message_start(path)
    GameData::Metadata::DATA.clear
    GameData::PlayerMetadata::DATA.clear
    storage_creator = []
    # Read from PBS file
    File.open(path, "rb") { |f|
      FileLineData.file = path   # For error reporting
      # Read a whole section's lines at once, then run through this code.
      # contents is a hash containing all the XXX=YYY lines in that section, where
      # the keys are the XXX and the values are the YYY (as unprocessed strings).
      pbEachFileSectionNumbered(f) { |contents, section_id|
        schema = (section_id == 0) ? GameData::Metadata::SCHEMA : GameData::PlayerMetadata::SCHEMA
        # Go through schema hash of compilable data and compile this section
        schema.each_key do |key|
          FileLineData.setSection(section_id, key, contents[key])   # For error reporting
          # Skip empty properties, or raise an error if a required property is
          # empty
          if contents[key].nil?
            if section_id == 0 && ["Home"].include?(key)
              raise _INTL("The entry {1} is required in {2} section 0.", key, path)
            end
            next
          end
          # Compile value for key
          value = pbGetCsvRecord(contents[key], key, schema[key])
          value = nil if value.is_a?(Array) && value.length == 0
          contents[key] = value
        end
        if section_id == 0   # Metadata
          # Construct metadata hash
          metadata_hash = {
            :id                  => section_id,
            :start_money         => contents["StartMoney"],
            :start_item_storage  => contents["StartItemStorage"],
            :home                => contents["Home"],
            :real_storage_creator     => contents["StorageCreator"],
            :wild_battle_BGM     => contents["WildBattleBGM"],
            :trainer_battle_BGM  => contents["TrainerBattleBGM"],
            :wild_victory_BGM    => contents["WildVictoryBGM"],
            :trainer_victory_BGM => contents["TrainerVictoryBGM"],
            :wild_capture_ME     => contents["WildCaptureME"],
            :surf_BGM            => contents["SurfBGM"],
            :bicycle_BGM         => contents["BicycleBGM"]
          }
          storage_creator[0] = contents["StorageCreator"]
          # Add metadata's data to records
          GameData::Metadata.register(metadata_hash)
        else   # Player metadata
          # Construct metadata hash
          metadata_hash = {
            :id                 => section_id,
            :trainer_type       => contents["TrainerType"],
            :walk_charset       => contents["WalkCharset"],
            :run_charset        => contents["RunCharset"],
            :cycle_charset      => contents["CycleCharset"],
            :cycle_stop_charset => contents["CycleStopCharset"],
            :surf_charset       => contents["SurfCharset"],
            :surf_jump_charset  => contents["SurfJumpCharset"],
            :dive_charset       => contents["DiveCharset"],
            :fish_charset       => contents["FishCharset"],
            :saving_charset     => contents["SavingCharset"],
            :watering_charset   => contents["WateringCharset"],
            :field_move_charset => contents["FieldMoveCharset"],
            :healing_charset    => contents["HealingCharset"],
            :surf_fish_charset  => contents["SurfFishCharset"]
          }
          # Add metadata's data to records
          GameData::PlayerMetadata.register(metadata_hash)
        end
      }
    }
    if !GameData::PlayerMetadata.exists?(1)
      raise _INTL("Metadata for player character 1 in {1} is not defined but should be.", path)
    end
    # Save all data
    GameData::Metadata.save
    GameData::PlayerMetadata.save
    process_pbs_file_message_end
  end
end
