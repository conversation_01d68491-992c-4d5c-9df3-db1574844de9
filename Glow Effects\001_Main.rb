class Spriteset_Map
  alias __glow__initialize initialize unless private_method_defined?(:__glow__initialize)
  def initialize(*args)
    __glow__initialize(*args)
    light_keys = []
    @map.events.each do |id, event|
      next if !pbEventCommentInput(event, 0, "GlowEvent")
      light_keys.push(id)
    end
    @lights = []
    light_keys.each do |key|
      event = @map.events[key]
      class_name = "GlowEffect_" + pbEventCommentInput(event, 1, "GlowEvent").first.split(",").first
      next if !Object.const_defined?(class_name)
      light = Object.const_get(class_name).new(@@viewport1, @map, event)
      @lights.push(light)
      if light.delete_event?
        @map.events.delete(key)
        idx = @character_sprites.index { |e| e.character == event }
        @character_sprites[idx].dispose
        @character_sprites.delete_at(idx)
      end
    end
    # Force an update to ensure lights are initialized properly
    @lights.each { |light| light.update }
    update
  end

  alias __glow__update update unless method_defined?(:__glow__update)
  def update(*args)
    __glow__update(*args)
    @lights&.each { |l| l.update }
  end

  alias __glow__dispose dispose unless method_defined?(:__glow__dispose)
  def dispose(*args)
    __glow__dispose(*args)
    @lights.each { |l| l.dispose }
    @lights.clear
  end
end

class GlowEffect < Sprite

  def delete_event?; return !@e_keep; end

  def initialize(viewport, map, event)
    super(viewport)
    @map     = map
    @event   = event
    @event_x = event.x
    @event_y = event.y
    @event_w = event.width
    @event_h = event.height
    options  = pbEventCommentInput(event, 2, "GlowEvent")[1]
    if options
      @options  = options.split(",").map! { |e| e.strip }
      @e_keep   = @options.include?("NoDelete")
      @recalc_z = @options.include?("NormalZ")
      @cave     = @options.include?("CaveLight")
      @x_offset = @options.find { |opt| opt.start_with?("XOffset") }&.split("=")&.last.to_i || 0
      @y_offset = @options.find { |opt| opt.start_with?("YOffset") }&.split("=")&.last.to_i || 0
    else
      @x_offset = 0
      @y_offset = 0
    end
    # Force immediate update during initialization
    self.visible = false  # Hide initially
    update
    self.visible = true if self.opacity > 0  # Show only if opacity > 0
  end

  def update
    super
    begin
      calc_x = (((@event_x * Game_Map::REAL_RES_X).to_f - @map.display_x) / Game_Map::X_SUBPIXELS).round
      calc_x += @event_w * Game_Map::TILE_WIDTH / 2
      calc_x += @x_offset  # Apply x offset
      self.x = calc_x
      calc_y = (((@event_y * Game_Map::REAL_RES_Y).to_f - @map.display_y) / Game_Map::Y_SUBPIXELS).round
      calc_y += Game_Map::TILE_HEIGHT
      calc_y += @y_offset  # Apply y offset
      self.y = calc_y
      self.ox = (Game_Map::TILE_WIDTH * @event_w) / 2
      self.oy = (Game_Map::TILE_HEIGHT * @event_h)
      self.z = @recalc_z ? (@event.screen_z || calc_y + Game_Map::TILE_HEIGHT - 1) : 999

      unless @cave  # If not a cave light, calculate the opacity based on time of day
        shade = PBDayNight.getShade
        if shade >= 160   # If light enough, call it fully day
          shade = 255
        elsif shade <= 68   # If dark enough, call it fully night
          shade = 0
        else
          shade = 255 - (255 * (160 - shade) / (160 - 32))
        end
        self.opacity = 255 - shade
      end

      self.visible = self.opacity > 0
    end
  end
end


class GlowEffect_Static < GlowEffect
  attr_accessor :fade_speed

  def initialize(viewport, map, event)
    super
    name = pbEventCommentInput(event, 1, "GlowEvent").first.split(",").last.strip
    self.bitmap = RPG::Cache.load_bitmap("Graphics/Characters/Glows/Static/", "Static_#{name}")
    self.bitmap.play if self.bitmap.animated?
    update
  end

  def update
    super
    return if !self.bitmap
    self.y  -= (Game_Map::TILE_HEIGHT * @event_h) / 2
    self.ox = self.bitmap.width / 2
    self.oy = self.bitmap.height / 2
  end
end

class GlowEffect_Fading < GlowEffect
  attr_accessor :fade_speed

  def initialize(viewport, map, event, fade_speed = 3)
    super(viewport, map, event)
    name = pbEventCommentInput(event, 1, "GlowEvent").first.split(",").last.strip
    self.bitmap = RPG::Cache.load_bitmap("Graphics/Characters/Glows/", "Fading_#{name}")
    self.bitmap.play if self.bitmap.animated?
    @fade_speed = fade_speed
    @current_opacity = 0
    @fade_direction = 1  # 1 for fading in, -1 for fading out
    update
  end

  def update
    super
    return if !self.bitmap
    self.y  -= (Game_Map::TILE_HEIGHT * @event_h) / 2
    self.ox = self.bitmap.width / 2
    self.oy = self.bitmap.height / 2
    @current_opacity += @fade_speed * @fade_direction
    @current_opacity = [[@current_opacity, 0].max, 255].min
    self.opacity = @current_opacity
    if @current_opacity <= 0 || @current_opacity >= 255
      @fade_direction *= -1
    end
  end
end

class GlowEffect_Pulsing < GlowEffect
  def initialize(viewport, map, event)
    @sprites = {}
    super
    @offset  = 0
    @name = pbEventCommentInput(event, 1, "GlowEvent").first.split(",").last.strip
    @speeds  = {}
    @toggles = {}
    3.times do |j|
      i  = 3 - j
      rf = 5 + rand(11)
      key = "circle_#{i}"
      @sprites[key] = Sprite.new(self.viewport)
      @sprites[key].bitmap = RPG::Cache.load_bitmap("Graphics/Characters/Glows/", "Pulsing_#{@name}_#{i}")
      @sprites[key].zoom_x = 1.0
      @sprites[key].zoom_y = 1.0
      @speeds[key]  = (i * (rf / 10000.0)) / (Graphics.frame_rate / 60.0)
      @toggles[key] = 1
    end
  end

  def update
    super
    3.times do |j|
      i = 3 - j
      key = "circle_#{i}"
      sprite = @sprites[key]
      next if !sprite
      sprite.x       = self.x
      sprite.y       = self.y - (Game_Map::TILE_HEIGHT * @event_h) / 2
      sprite.z       = self.z + j
      sprite.ox      = self.ox - ((Game_Map::TILE_WIDTH * @event_w) / 2) + sprite.bitmap.width / 2
      sprite.oy      = self.oy - (Game_Map::TILE_HEIGHT * @event_h) + sprite.bitmap.height / 2
      sprite.visible = self.visible
      sprite.opacity = self.opacity # Apply the calculated opacity
      sprite.zoom_x += @speeds[key] * @toggles[key]
      sprite.zoom_y += @speeds[key] * @toggles[key]
      @toggles[key] *= -1 if sprite.zoom_x <= 0.95 || sprite.zoom_x >= 1.05
    end
  end

  def dispose
    super
    pbDisposeSpriteHash(@sprites)
  end
end