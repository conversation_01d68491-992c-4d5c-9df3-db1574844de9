#-------------------------------------------------------------------------------
# Expose follower_sprites attribute of Spriteset_Map
#-------------------------------------------------------------------------------
class Spriteset_Global
  attr_reader :follower_sprites
end

#-------------------------------------------------------------------------------
# Add Reflections to Following Pokemon sprite
#-------------------------------------------------------------------------------
class Sprite_Character
  def set_reflection(viewport, event)
    @reflection = Sprite_Reflection.new(self, viewport) if !@reflection
  end
end

#-------------------------------------------------------------------------------
# Refresh Following Pokemon sprites whenever the map is refreshed
#-------------------------------------------------------------------------------
EventHandlers.add(:on_enter_map, :erase_following_pkmn, proc { |_old_map_id|
  event = FollowingPkmn.get_data
  next if !event
  FollowingPkmn.refresh
  $map_factory.maps.each { |map|
    map.events[event.event_id]&.erase if event.original_map_id == event.current_map_id
  }
})

class FollowerSprites
  #-----------------------------------------------------------------------------
  # Updating the refresh method to allow clearing of base event in all maps,
  # add reflections and prevent crash when base map/event is deleted
  #-----------------------------------------------------------------------------
  alias __followingpkmn__refresh refresh unless method_defined?(:__followingpkmn__refresh)
  def refresh(*args)
    ret = __followingpkmn__refresh(*args)
    return ret if !FollowingPkmn.can_check?
    event = FollowingPkmn.get_event
    @sprites.each do |spr|
      next if !FollowingPkmn.get_data&.following_pkmn?
      spr.set_reflection(@viewport, event)
    end
    data = FollowingPkmn.get_data
    $map_factory.maps.each { |map|
      map&.events[data.event_id]&.erase if data && data.original_map_id == data.current_map_id
    }
    FollowingPkmn.refresh
  end
  #-----------------------------------------------------------------------------
  # Adding DayNight and Status condition pulsing effect to Following Pokemon
  # sprite
  #-----------------------------------------------------------------------------
  OPACITY_FADE_DURATION = 0.75
  OPACITY_LINGER_DURATION = 1.0


  alias __followingpkmn__update update unless method_defined?(:__followingpkmn__update)
  def update(*args)
    __followingpkmn__update(*args)
    return if !FollowingPkmn.active?
    @sprites.each_with_index do |sprite, i|
      next if !$PokemonGlobal.followers[i] || !$PokemonGlobal.followers[i].following_pkmn?
      first_pkmn = FollowingPkmn.get_pokemon
      next if !first_pkmn
      if (first_pkmn.status == :NONE || !FollowingPkmn::APPLY_STATUS_TONES) && !sprite.follower_animating
        sprite.color.set(0, 0, 0, 0)
        $game_temp.status_pulse = nil
        next
      end
      status_tone = nil
      status_tone = FollowingPkmn.const_get("TONE_#{first_pkmn.status}") if FollowingPkmn.const_defined?("TONE_#{first_pkmn.status}")
      next if !status_tone || status_tone.none? { |s| s > 0 }
      pulse    = $game_temp.status_pulse
      now_time = System.uptime
      opacity  = lerp(pulse[:start], pulse[:end], OPACITY_LINGER_DURATION, pulse[:timer], now_time)
      sprite.color.set(status_tone[0], status_tone[1], status_tone[2], opacity)
      if (now_time - pulse[:timer]) > OPACITY_FADE_DURATION + OPACITY_LINGER_DURATION
        $game_temp.status_pulse[:end], $game_temp.status_pulse[:start] = $game_temp.status_pulse[:start], $game_temp.status_pulse[:end]
        $game_temp.status_pulse[:timer] = System.uptime
      end
    end
  end
  #-----------------------------------------------------------------------------
  # Add emote animation to Following Pokemon
  #-----------------------------------------------------------------------------
  def set_animation(anim_id)
    @sprites.each do |spr|
      next if spr.character != FollowingPkmn.get_event
      spr.character.animation_id = anim_id
    end
  end
  #-----------------------------------------------------------------------------
end
