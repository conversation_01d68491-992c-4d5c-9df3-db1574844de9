class Game_Character
  attr_accessor :pattern

  def shows_shadow?
    return true if !defined?(OWShadowSettings)
    return false if nil_or_empty?(@character_name) || @transparent
    if OWShadowSettings::CASE_SENSITIVE_BLACKLISTS
      return false if OWShadowSettings::SHADOWLESS_CHARACTER_NAME.any?{|e| @character_name[/#{e}/]}
      return false if self != $game_player && OWShadowSettings::SHADOWLESS_EVENT_NAME.any? {|e| self.name[/#{e}/]}
    else
      return false if OWShadowSettings::SHADOWLESS_CHARACTER_NAME.any?{|e| @character_name[/#{e}/i]}
      return false if self != $game_player && OWShadowSettings::SHADOWLESS_EVENT_NAME.any? {|e| self.name[/#{e}/i]}
    end
    return true
  end

  alias __hgss_turning__turn_generic turn_generic unless method_defined?(:__hgss_turning__turn_generic)
  def turn_generic(dir)
    begin
      old_dir = @direction
      __hgss_turning__turn_generic(dir)
      return if old_dir == @direction || @pattern != 0 || !@walk_anime || !shows_shadow?
      @pattern = 3
    rescue => e
      # Fallback if there's an error
      Console.echo_error("Error in turn_generic: #{e.message}")
      __hgss_turning__turn_generic(dir) rescue nil
    end
  end

  alias __hgss_turning__update_stop update_stop unless method_defined?(:__hgss_turning__update_stop)
  def update_stop
    begin
      __hgss_turning__update_stop
      return if @step_anime || @pattern == @original_pattern
      # In v21.1, @anime_count is in seconds, not frames
      # Add a small amount of time (0.05 seconds) instead of 1.5 frames
      @anime_count += 0.05
    rescue => e
      # Fallback if there's an error
      Console.echo_error("Error in update_stop: #{e.message}")
      __hgss_turning__update_stop rescue nil
    end
  end

  alias __hgss_turning__update update unless method_defined?(:__hgss_turning__update)
  def update
    begin
      __hgss_turning__update
      return if !$game_temp || !$game_temp.respond_to?(:in_menu) || !$game_temp.in_menu

      # In v21.1, @anime_count is in seconds, not frames
      # Convert the frame-based check to time-based
      # Original was: return if @anime_count <= 18 - (@move_speed || 4) * 3
      # For move_speed 4, this was: return if @anime_count <= 6 frames
      # At 60 FPS, 6 frames = 0.1 seconds
      time_threshold = 0.1 * (6.0 / (18 - (@move_speed || 4) * 3))
      return if @anime_count <= time_threshold

      if !@step_anime && @stop_count > 0
        @pattern = @original_pattern
      else
        @pattern = (@pattern + 1) % 4
      end
      @anime_count = 0
    rescue => e
      # Fallback if there's an error
      Console.echo_error("Error in update: #{e.message}")
      __hgss_turning__update rescue nil
    end
  end
end
