class Game_Character
  alias __gen4_anim__update_pattern update_pattern unless method_defined?(:__gen4_anim__update_pattern)
  def update_pattern
    return if @lock_pattern
    # Character has stopped moving, return to original pattern
    if @moved_last_frame && !@moved_this_frame && !@step_anime
      @pattern = @original_pattern
      @anime_count = 0
      return
    end
    # Character has started to move, change pattern immediately
    if !@moved_last_frame && @moved_this_frame && !@step_anime
      @pattern = (@pattern + 1) % 4 if @walk_anime
      @anime_count = 0
      return
    end
    # Calculate how many frames each pattern should display for
    pattern_time = pattern_update_speed / 4   # 4 frames per cycle in a charset
    return if @anime_count < pattern_time
    # Advance to the next animation frame
    @pattern = (@pattern + 1) % 4
    @anime_count -= pattern_time
  end

  # Only define this method if it doesn't already exist
  unless method_defined?(:pattern_update_speed)
    def pattern_update_speed
      return @jump_time * 2 if jumping?
      ret = @move_time * 2
      ret *= 2 if @move_speed >= 5   # Cycling speed or faster; slower animation
      return ret
    end
  end
end
