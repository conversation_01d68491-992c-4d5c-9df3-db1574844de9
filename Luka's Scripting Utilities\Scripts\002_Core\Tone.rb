#===============================================================================
#  Extensions for the `Tone` class
#===============================================================================
class ::Tone
  include ::LUTS::Concerns::Animatable

  def update; end
  #-----------------------------------------------------------------------------
  #  gets value of all
  #-----------------------------------------------------------------------------
  def all
    (red + green + blue) / 3
  end
  #-----------------------------------------------------------------------------
  #  applies value to all channels
  #-----------------------------------------------------------------------------
  def all=(val)
    self.red   = val
    self.green = val
    self.blue  = val
  end
  #-----------------------------------------------------------------------------
end
